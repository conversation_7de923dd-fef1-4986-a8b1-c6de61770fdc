/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo .logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: #00bf62;
    color: white;
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(0deg, #1669ae 0%, rgba(44, 204, 124, 0.17) 70%);
    opacity: 1;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 2;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 191, 98, 0.8), rgba(22, 105, 174, 0.8));
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-text {
    max-width: 800px;
    color: white;
    text-align: center;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease 0.3s both;
}

/* Services Section */
.services {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 70%;
    background: linear-gradient(180deg, #1669ae 0%, rgba(44, 204, 124, 0.17) 70%);
    opacity: 1;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 1;
}

.services .container {
    position: relative;
    z-index: 2;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 4rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #00bf62, #1669ae);
    border-radius: 2px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-icon {
    position: relative;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    overflow: hidden;
}

.service-icon svg {
    width: 120px;
    height: 120px;
    transition: transform 0.3s ease;
}

.service-card:hover .service-icon svg {
    transform: scale(1.1);
}

.service-content {
    padding: 2rem;
}

.service-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.service-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.service-link {
    display: inline-block;
    background: #00bf62;
    color: white;
    padding: 0.5rem 1.5rem;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.service-link:hover {
    background: #00a855;
    transform: translateY(-2px);
}

/* About Section */
.about {
    padding: 100px 0;
    background: white;
    position: relative;
    overflow: hidden;
}

.about::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 60%;
    height: 80%;
    background: linear-gradient(135deg, transparent 30%, #1669ae 100%);
    opacity: 0.08;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 1;
}

.about .container {
    position: relative;
    z-index: 2;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-subtitle {
    color: #00bf62;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.about-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #333;
    line-height: 1.2;
}

.about-description {
    color: #666;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.cta-button {
    display: inline-block;
    background: #FFD700;
    color: #333;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: #FFC107;
    transform: translateY(-2px);
}

.about-image {
    position: relative;
    height: 400px;
    background: url('../images/hero_image.jpeg') center/cover;
    border-radius: 15px;
    display: flex;
    align-items: flex-end;
    padding: 2rem;
}

.energy-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.energy-subtitle {
    color: #00bf62;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    letter-spacing: 2px;
}

.energy-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

/* Brand Logos Carousel */
.brands-carousel {
    padding: 80px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.brands-carousel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1669ae 0%, rgba(44, 204, 124, 0.17) 50%, transparent 80%);
    opacity: 0.06;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 1;
}

.carousel-container {
    position: relative;
    z-index: 2;
    overflow: hidden;
    margin-top: 3rem;
}

.carousel-track {
    display: flex;
    animation: scroll 30s linear infinite;
    width: calc(200% + 4rem);
}

.brand-slide {
    flex: 0 0 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 15px;
    margin-right: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.brand-slide:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.brand-slide img {
    max-width: 150px;
    max-height: 80px;
    object-fit: contain;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.brand-slide:hover img {
    filter: grayscale(0%);
}

@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* Solutions Section */
.solutions {
    padding: 100px 0;
    background: linear-gradient(135deg, #1669ae, #0d4f8c);
    color: white;
    position: relative;
    overflow: hidden;
}

.solutions::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(270deg, rgba(44, 204, 124, 0.15) 0%, transparent 70%);
    opacity: 1;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 1;
}

.solutions .container {
    position: relative;
    z-index: 2;
}

.solutions-header {
    text-align: center;
    margin-bottom: 4rem;
}

.solutions-subtitle {
    color: #FFD700;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.solutions-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
}

.solutions-description {
    font-size: 1.1rem;
    max-width: 800px;
    margin: 0 auto;
    opacity: 0.9;
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.solution-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.solution-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
}

.solution-image {
    height: 200px;
    overflow: hidden;
}

.solution-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.solution-card:hover .solution-image img {
    transform: scale(1.1);
}

.solution-content {
    padding: 2rem;
}

.solution-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.solution-description {
    opacity: 0.9;
    line-height: 1.6;
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.why-choose-us::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background: linear-gradient(180deg, #1669ae 0%, rgba(44, 204, 124, 0.17) 60%);
    opacity: 0.12;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 1;
}

.why-choose-us .container {
    position: relative;
    z-index: 2;
}

.why-choose-content {
    text-align: center;
}

.why-choose-subtitle {
    color: #00bf62;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.why-choose-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #333;
}

.why-choose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.why-choose-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.why-choose-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.why-choose-item-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.why-choose-item-description {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: white;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(180deg, #1669ae 0%, rgba(44, 204, 124, 0.17) 70%);
    opacity: 0.05;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    z-index: 1;
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #333;
}

.contact-info p {
    color: #666;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: #666;
}

.contact-item i {
    color: #00bf62;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.contact-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00bf62;
}

.submit-btn {
    background: #00bf62;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-btn:hover {
    background: #00a855;
    transform: translateY(-2px);
}

/* WhatsApp Contact Section */
.whatsapp-contact {
    display: flex;
    justify-content: center;
    align-items: center;
}

.whatsapp-card {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(37, 211, 102, 0.3);
    position: relative;
    overflow: hidden;
}

.whatsapp-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: pulse 3s ease-in-out infinite;
}

.whatsapp-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 2s ease-in-out infinite;
}

.whatsapp-card h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.whatsapp-card p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.whatsapp-arrow {
    width: 60px;
    height: 30px;
    margin: 1rem auto;
    animation: slideRight 2s ease-in-out infinite;
}

.whatsapp-btn-large {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    color: #25D366;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.whatsapp-btn-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    background: #f8f9fa;
}

.whatsapp-btn-large i {
    font-size: 1.3rem;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes slideRight {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(10px); }
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 40px;
    margin-bottom: 1rem;
}

.footer-logo p {
    color: #ccc;
    line-height: 1.6;
}

.footer-links h4,
.footer-contact h4 {
    margin-bottom: 1rem;
    color: #00bf62;
}

.footer-links ul {
    list-style: none;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #00bf62;
}

.footer-contact p {
    color: #ccc;
    margin-bottom: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* WhatsApp Button */
.whatsapp-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.whatsapp-btn a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #25D366;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
}

.whatsapp-btn a:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .solutions-title,
    .about-title,
    .why-choose-title {
        font-size: 2rem;
    }

    .services-grid,
    .solutions-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 15px;
    }
}

